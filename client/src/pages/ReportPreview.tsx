import { useEffect, useState } from "react";
import { useParams, useLocation } from "wouter";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";

interface PreviewDataItem {
  title: string;
  snippet: string;
}

interface PreviewData {
  analysisId: number;
  projectName: string;
  scorePreview: {
    total: number;
    category: string;
    topStrength: string;
    topWeakness: string;
  };
  testimonial: string;
  article: {
    title: string;
    snippet: string;
  };
  tip: string;
}

export default function ReportPreview() {
  const [, setLocation] = useLocation();
  const params = useParams<{ id: string }>();
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [previewData, setPreviewData] = useState<PreviewData | null>(null);
  const [personalizationForm, setPersonalizationForm] = useState({
    primaryGoal: "User acquisition",
    biggestChallenge: "Getting user feedback",
    userCount: "5-10"
  });
  const [personalizationTip, setPersonalizationTip] = useState<string | null>(null);
  const [tipLoading, setTipLoading] = useState(false);

  useEffect(() => {
    const fetchPreviewData = async () => {
      try {
        const response = await apiRequest(
          "GET",
          `/api/report/preview/${params.id}`
        );
        
        const responseData = await response.json();
        if (response.ok && responseData.status === "success") {
          console.log("Preview data:", responseData.data);
          setPreviewData(responseData.data);
        } else {
          console.error("API error:", responseData);
          toast({
            title: "Error",
            description: "Failed to load report preview",
            variant: "destructive"
          });
        }
      } catch (error) {
        console.error("Error loading preview:", error);
        toast({
          title: "Error",
          description: "Failed to load report preview",
          variant: "destructive"
        });
      } finally {
        setLoading(false);
      }
    };

    if (params.id) {
      fetchPreviewData();
    }
  }, [params.id, toast]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setPersonalizationForm(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleRadioChange = (name: string, value: string) => {
    setPersonalizationForm(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const generatePersonalizedTip = async () => {
    setTipLoading(true);
    try {
      const response = await apiRequest(
        'POST',
        '/api/report/generate-tip',
        {
          projectId: params.id,
          formData: personalizationForm
        }
      );
      
      const responseData = await response.json();
      console.log("Tip response:", responseData);
      
      if (response.ok && responseData.status === "success") {
        setPersonalizationTip(responseData.data?.tip);
      } else {
        toast({
          title: "Error",
          description: "Failed to generate personalized tip",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error("Error generating tip:", error);
      toast({
        title: "Error",
        description: "Failed to generate personalized tip",
        variant: "destructive"
      });
    } finally {
      setTipLoading(false);
    }
  };

  const proceedToPayment = () => {
    setLocation(`/report/payment/${params.id}`);
  };

  if (loading) {
    return (
      <div className="max-w-6xl mx-auto p-6">
        <Card className="p-8">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
          </div>
        </Card>
      </div>
    );
  }

  if (!previewData || !previewData.scorePreview) {
    return (
      <div className="max-w-6xl mx-auto p-6">
        <Card className="p-8">
          <h2 className="text-2xl font-bold text-center mb-4">Report Preview Not Found</h2>
          <p className="text-center text-neutral-600">
            We couldn't find a preview for this report. Please go back and try again.
          </p>
          <div className="flex justify-center mt-6">
            <Button onClick={() => setLocation("/")}>
              Back to Home
            </Button>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="mb-8 text-center">
        <h1 className="text-3xl font-bold text-gray-900">
          Premium Report Preview
        </h1>
        <p className="text-lg text-gray-600 mt-2">
          Get a glimpse of what's included in your full report for {previewData.projectName}
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {/* Score Preview Card */}
        <Card className="p-6 bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-200">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold text-blue-800">MVP Readiness Score</h2>
            <div className="bg-white rounded-full h-16 w-16 flex items-center justify-center border-2 border-blue-400">
              <span className="text-2xl font-bold text-blue-700">{previewData.scorePreview.total}</span>
            </div>
          </div>

          <p className="text-blue-700 font-medium mb-4">
            Your project is in the <span className="font-bold">{previewData.scorePreview.category}</span> category
          </p>

          <div className="space-y-2">
            <p className="text-sm text-blue-800">
              <span className="font-medium">Top Strength:</span> {previewData.scorePreview.topStrength}
            </p>
            <p className="text-sm text-blue-800">
              <span className="font-medium">Top Area to Improve:</span> {previewData.scorePreview.topWeakness}
            </p>
          </div>

          <p className="mt-4 text-sm text-blue-600 italic">
            The full report contains detailed scoring across 8 criteria with code-level insights
          </p>
        </Card>

        {/* Purchase Card */}
        <Card className="p-6 bg-gradient-to-br from-primary-50 to-primary-100 border-primary-200">
          <h3 className="text-xl font-bold text-primary-900 mb-4">Unlock Your Full Report</h3>
          
          <ul className="space-y-3 mb-6">
            <li className="flex items-start">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-primary-600 mr-2 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
              <span className="text-primary-800">25+ pages of personalized insights</span>
            </li>
            <li className="flex items-start">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-primary-600 mr-2 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
              <span className="text-primary-800">Code-level improvement suggestions</span>
            </li>
            <li className="flex items-start">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-primary-600 mr-2 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
              <span className="text-primary-800">Personalized roadmap & timeline</span>
            </li>
            <li className="flex items-start">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-primary-600 mr-2 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
              <span className="text-primary-800">Resource library & templates</span>
            </li>
          </ul>

          <div className="bg-white rounded-lg p-4 mb-6">
            <div className="flex justify-between items-center">
              <span className="text-gray-700 font-medium">Report Price:</span>
              <span className="text-primary-900 font-bold text-xl">$9.99</span>
            </div>
            <p className="text-gray-500 text-xs mt-1">One-time payment, instant delivery</p>
          </div>

          <Button 
            onClick={proceedToPayment} 
            className="w-full py-6"
            size="lg"
          >
            Unlock Full Report
          </Button>
        </Card>
      </div>

      {/* Testimonial Card */}
      <Card className="p-6 bg-white mt-8">
        <div className="flex items-start space-x-4">
          <div className="rounded-full bg-gray-200 h-12 w-12 flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
          </div>
          <div>
            <p className="text-gray-800 italic">"{previewData.testimonial}"</p>
            <p className="text-gray-600 mt-2 text-sm">— Alex K., Frontend Developer</p>
          </div>
        </div>
      </Card>
    </div>
  );
}