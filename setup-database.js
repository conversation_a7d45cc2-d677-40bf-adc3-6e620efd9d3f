// Simple database setup script
// Run with: node setup-database.js

const { drizzle } = require('drizzle-orm/postgres-js');
const postgres = require('postgres');
const { analyses } = require('./shared/schema');

async function setupDatabase() {
  try {
    console.log('🔧 Setting up database...');
    
    // Check if DATABASE_URL is set
    if (!process.env.DATABASE_URL) {
      console.log('❌ DATABASE_URL not found in environment variables');
      console.log('📝 Please set DATABASE_URL in your .env file');
      console.log('   Example: DATABASE_URL=postgresql://user:password@localhost:5432/mvp_score_db');
      return;
    }
    
    // Connect to database
    const client = postgres(process.env.DATABASE_URL);
    const db = drizzle(client);
    
    console.log('✅ Connected to database');
    
    // Create a test analysis to verify the table exists
    const testAnalysis = {
      type: 'file',
      projectType: 'web-app',
      status: 'completed',
      description: 'Test analysis for database setup'
    };
    
    const result = await db.insert(analyses).values(testAnalysis).returning();
    console.log('✅ Database tables are working');
    console.log('📊 Created test analysis with ID:', result[0].id);
    
    // Clean up test data
    await db.delete(analyses).where(analyses.id.eq(result[0].id));
    console.log('🧹 Cleaned up test data');
    
    await client.end();
    console.log('✅ Database setup complete!');
    
  } catch (error) {
    console.error('❌ Database setup failed:', error.message);
    
    if (error.message.includes('relation "analyses" does not exist')) {
      console.log('📝 Run database migrations first:');
      console.log('   npx drizzle-kit generate');
      console.log('   npx drizzle-kit migrate');
    }
  }
}

// Load environment variables
require('dotenv').config();

// Run setup
setupDatabase();
